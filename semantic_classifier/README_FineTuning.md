# Fine-tuning SentenceTransformer

Skript pro fine-tuning sentence transformer modelů pomocí triplet loss na vlastních datech.

## Vlastnosti

- **Nezávislý na SBertClassifier** - nevyžaduje úpravy existující třídy
- **<PERSON>k<PERSON> na<PERSON> dat** - použív<PERSON> `data_util.generate_triplet_dataset`
- **Triplet Loss** - optimalizuje model pro lepší separaci pozitivních/negativních příkladů
- **Evaluace** - automatické porovnání s původním modelem
- **Flexibilní** - nastavitelné parametry trénování

## Použití

### Základní fine-tuning

```bash
# S výchozími parametry
python3 semantic_classifier/fine_tune_sbert.py

# S vlastními daty
python3 semantic_classifier/fine_tune_sbert.py --data path/to/training_data.xlsx

# S vlastními parametry
python3 semantic_classifier/fine_tune_sbert.py \
    --data semantic_classifier/training_data/training_set.xlsx \
    --output ./my_fine_tuned_model \
    --epochs 20 \
    --batch-size 32 \
    --device cuda
```

### Pouze evaluace existujícího modelu

```bash
python3 semantic_classifier/fine_tune_sbert.py \
    --evaluate-only ./fine_tuned_sbert \
    --data semantic_classifier/training_data/training_set.xlsx
```

## Parametry

| Parametr | Výchozí | Popis |
|----------|---------|-------|
| `--data` | `semantic_classifier/training_data/training_set.xlsx` | Cesta k tréninkovým datům |
| `--output` | `./fine_tuned_sbert` | Výstupní složka pro model |
| `--epochs` | `10` | Počet epoch |
| `--batch-size` | `16` | Velikost batch |
| `--device` | `cpu` | Zařízení (cpu/cuda/mps) |
| `--evaluate-only` | - | Pouze evaluace (cesta k modelu) |

## Formát dat

Skript očekává Excel soubor ve formátu kompatibilním s `load_training_data_from_xlsx`:

```
Sheet "Kategorie1":
| anchor_triplet | positives_variants | negatives |
|----------------|-------------------|-----------|
| kotva text     |                   |           |
|                | pozitivní 1       |           |
|                | pozitivní 2       |           |
|                |                   | negativní 1 |
|                |                   | negativní 2 |
```

## Výstup

Po dokončení fine-tuningu se vytvoří:

```
fine_tuned_sbert/
├── config.json              # Konfigurace modelu
├── pytorch_model.bin         # Váhy modelu
├── sentence_bert_config.json # SentenceTransformer konfigurace
├── tokenizer_config.json     # Tokenizer konfigurace
├── vocab.txt                 # Slovník
└── ...                       # Další soubory
```

## Programové použití

```python
from semantic_classifier.fine_tune_sbert import SBertFineTuner

# Inicializace
fine_tuner = SBertFineTuner(device="cpu")

# Fine-tuning
output_dir = fine_tuner.fine_tune(
    xlsx_path="training_data.xlsx",
    output_dir="./my_model",
    epochs=15,
    batch_size=32
)

# Evaluace
fine_tuner.evaluate_model(output_dir, "test_data.xlsx")
```

## Použití vyladěného modelu

### S SBertClassifier

```python
from semantic_classifier.SBertClassifier import SBertClassifier

# Použij vyladěný model místo výchozího
classifier = SBertClassifier(
    model_name="./fine_tuned_sbert",  # Cesta k vyladěnému modelu
    model_dir="./fine_tuned_sbert"
)

# Zbytek funguje stejně
classifier.create_and_save_prototypes(class_texts)
result = classifier.classify("test text")
```

### Přímo s SentenceTransformer

```python
from sentence_transformers import SentenceTransformer

# Načti vyladěný model
model = SentenceTransformer("./fine_tuned_sbert")

# Použij pro embeddingy
embeddings = model.encode(["text1", "text2"])
```

## Triplet Loss

Skript používá **Triplet Loss** pro optimalizaci:

- **Anchor**: referenční text (kotva z Excel)
- **Positive**: pozitivní příklad (měl by být podobný anchor)
- **Negative**: negativní příklad (měl by být odlišný od anchor)

**Cíl**: Minimalizovat vzdálenost anchor-positive, maximalizovat anchor-negative.

## Evaluace

Automatická evaluace zahrnuje:

1. **TripletEvaluator** - měří kvalitu triplet separace
2. **Porovnání embeddingů** - kosinusová podobnost před/po fine-tuningu
3. **Test na ukázkových textech** - praktické porovnání

## Tipy pro lepší výsledky

### Kvalita dat
- **Vyvážené třídy** - podobný počet pozitivních/negativních příkladů
- **Rozmanité příklady** - různé formulace stejného významu
- **Kvalitní negativy** - skutečně odlišné, ale relevantní texty

### Parametry trénování
- **Malé datasety** (< 1000 tripletů): epochs=5-10, batch_size=8-16
- **Střední datasety** (1000-10000): epochs=10-20, batch_size=16-32
- **Velké datasety** (> 10000): epochs=3-5, batch_size=32-64

### Hardware
- **CPU**: batch_size=8-16, může být pomalé
- **GPU**: batch_size=32-64, výrazně rychlejší
- **Apple Silicon**: device="mps", batch_size=16-32

## Příklady výsledků

**Před fine-tuningem:**
```
'číslo faktury' vs 'invoice number': 0.756
'datum vystavení' vs 'issue date': 0.698
```

**Po fine-tuningu:**
```
'číslo faktury' vs 'invoice number': 0.892
'datum vystavení' vs 'issue date': 0.834
```

## Řešení problémů

### Chyba paměti
```bash
# Zmenši batch size
--batch-size 8

# Nebo použij CPU
--device cpu
```

### Pomalé trénování
```bash
# Použij GPU
--device cuda

# Nebo zmenši počet epoch
--epochs 5
```

### Špatné výsledky
- Zkontroluj kvalitu trénovacích dat
- Zvyš počet epoch
- Zkus jiný base model
