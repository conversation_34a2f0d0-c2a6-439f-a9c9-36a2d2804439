# SBertClassifier

Minimalistická třída pro sémantickou klasifikaci kompatibilní s C++.

## Vlastnosti

- **Automatická inicializace**: Po vytvoření instance se automaticky načte model a prototypy (pokud existují)
- **Efektivní**: Používá normalizované embeddingy a vektorizované operace
- **Normalizované skóre**: Všechny metody vracejí skóre podobnosti v rozsahu 0-1
- **C++ kompatibilní**: Všechny operace jsou přenositelné do C++ (numpy arrays, kosinusová podobnost)
- **Minimalistická**: Pouze nezbytné metody bez složitých závislostí

## Použití

### Základní inicializace

```python
from semantic_classifier.SBertClassifier import SBertClassifier

# Automaticky načte model a prototypy
classifier = SBertClassifier()
```

### Vytvoření prototypů

```python
# Definice trénovacích dat
class_texts = {
    "Faktura": ["faktura", "číslo faktury", "invoice number"],
    "Datum": ["datum", "datum vystavení", "date", "datum splatnosti"],
    "Částka": ["částka", "celkem", "amount", "total", "suma"]
}

# Vytvoření a uložení prototypů
classifier.create_and_save_prototypes(class_texts)
```

### Klasifikace

```python
# Jednotlivá klasifikace
class_id, confidence = classifier.classify("číslo faktury 2024001")
class_name = classifier.get_class_name(class_id)
print(f"Třída: {class_name}, Confidence: {confidence:.3f}")

# Batch klasifikace (rychlejší pro více textů)
texts = ["faktura 123", "datum 15.1.2024", "celkem 1500 Kč"]
results = classifier.classify_batch(texts)
for text, (class_id, confidence) in zip(texts, results):
    class_name = classifier.get_class_name(class_id)
    print(f"'{text}' -> {class_name} ({confidence:.3f})")
```

### Výpočet embeddingů a podobnosti

```python
# Embeddingy
embeddings = classifier.encode(["text1", "text2", "text3"])
print(f"Shape: {embeddings.shape}")  # (3, 768)

# Podobnost dvou textů (0-1)
similarity = classifier.similarity("faktura", "invoice")
print(f"Podobnost: {similarity:.3f}")  # např. 0.978
```

### Export pro C++

```python
# Rychlý export (pouze prototypy a metadata)
classifier.export_for_cpp("./cpp_export", export_model=False)

# Kompletní export (včetně ONNX modelu - pomalé)
classifier.export_for_cpp("./cpp_export", export_model=True)
```

Exportované soubory:
- `prototypes.bin` - raw float32 matice prototypů
- `prototypes_dims.txt` - rozměry matice (num_classes embedding_dim)
- `prototypes_simple.bin` - prototypy s názvy tříd (kompatibilní formát)
- `model_config.h` - C++ header s konstantami a strukturami
- `tokenizer/` - tokenizer soubory
- `text_encoder.onnx` - ONNX model (volitelně)

## API Reference

### Konstruktor

```python
SBertClassifier(model_name, model_dir, device)
```

- `model_name`: název modelu na HuggingFace (default: paraphrase-multilingual-mpnet-base-v2)
- `model_dir`: cesta k lokální složce s modelem (default: ./semantic_classifier/mpnet_ok)
- `device`: 'cpu', 'cuda' nebo 'mps' (default: 'cpu')

### Hlavní metody

#### `encode(texts) -> np.ndarray`
Vypočítá normalizované embeddingy pro texty.

#### `similarity(text1, text2) -> float`
Kosinusová podobnost mezi dvěma texty, normalizovaná do rozsahu 0-1.

#### `create_and_save_prototypes(class_texts)`
Vytvoří prototypy z trénovacích dat a uloží je.

#### `classify(text, return_confidence=True) -> Tuple[int, float]`
Klasifikuje jeden text, vrací (class_id, normalized_confidence) kde confidence je v rozsahu 0-1.

#### `classify_batch(texts) -> List[Tuple[int, float]]`
Vektorizovaná klasifikace více textů najednou, confidence normalizované do rozsahu 0-1.

#### `get_class_name(class_id) -> str`
Převede ID třídy na název.

#### `get_class_id(class_name) -> int`
Převede název třídy na ID.

#### `export_for_cpp(base_path, export_model=True)`
Kompletní export pro C++ včetně prototypů, metadat a volitelně ONNX modelu.

#### `info()`
Vypíše informace o klasifikátoru.

## Formáty C++ exportu

### Raw prototypy (`prototypes.bin`)
```cpp
// Raw float32 matice: [num_classes * embedding_dim]
// Načtení: fread(data, sizeof(float), num_classes * embedding_dim, file)
// Rozměry v prototypes_dims.txt: "num_classes embedding_dim"
```

### Jednoduché prototypy (`prototypes_simple.bin`)
```
[4 bytes] počet_tříd (little endian)
[4 bytes] rozměr_embeddingů (little endian)
Pro každou třídu:
  [4 bytes] délka_názvu (little endian)
  [N bytes] název_třídy (UTF-8)
  [768*4 bytes] embedding (float32 array)
```

### C++ header (`model_config.h`)
Obsahuje konstanty, názvy tříd, struktury a příklady použití.

## Výkonnost

- **Embeddingy**: ~1000 textů/sekunda (CPU)
- **Klasifikace**: ~5000 textů/sekunda (batch)
- **Paměť**: ~75 KB pro 25 tříd (prototypy)
- **Model**: ~420 MB (paraphrase-multilingual-mpnet-base-v2)

## Příklady

Viz soubory:
- `test_sbert_classifier.py` - základní testy
- `example_sbert_usage.py` - použití s reálnými daty

## Normalizace skóre

Všechny metody vracejí skóre podobnosti normalizované do rozsahu **0-1**:
- **0.0**: minimální podobnost (zcela odlišné texty)
- **1.0**: maximální podobnost (identické texty)
- **Transformace**: `(cosine_similarity + 1.0) / 2.0` s clampingem na [0, 1]

## Kompatibilita s C++

Všechny operace jsou navrženy pro snadný port do C++:
- Numpy arrays → std::vector<float>
- Kosinusová podobnost → dot product normalizovaných vektorů
- Normalizace skóre → `std::max(0.0f, std::min(1.0f, (score + 1.0f) / 2.0f))`
- Binární export → memcpy načítání
- Žádné Python-specifické závislosti v core algoritmech
