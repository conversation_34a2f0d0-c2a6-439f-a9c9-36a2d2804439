import pandas as pd
from semantic_classifier.training_data_preparation import load_training_data_from_xlsx
from SBertClassifier import SBertClassifier
def generate_triplet_dataset(xlsx_path):
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)
    triplets = []

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']

        n_pos = len(positives)
        n_neg = len(negatives)
        max_count = max(n_pos, n_neg)

        # Vytvoříme všechny kombinace každý s každým
        pairs = [(p, n) for p in positives for n in negatives]

        if max_count < 5:
            selected_pairs = pairs
        elif max_count <= 15:
            selected_pairs = pairs[:100]
        else:
            selected_pairs = pairs[:200]

        for positive, negative in selected_pairs:
            triplets.append({
                'anchor': anchor,
                'positive': positive,
                'negative': negative
            })

    df = pd.DataFrame(triplets)
    return df


def generate_smart_triplet_dataset(xlsx_path, positive_threshold=0.85, negative_threshold=0.6):
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)
    triplets = []
    scorer = SBertClassifier()

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']

        # Oskórování a filtrování pozitiv
        scored_positives = [(p, scorer.similarity(anchor, p)) for p in positives]
        filtered_positives = [p for p, s in scored_positives if s <= positive_threshold]
        filtered_positives = sorted(filtered_positives, key=lambda p: next(s for pp, s in scored_positives if pp == p))

        # Oskórování a filtrování negativ
        scored_negatives = [(n, scorer.similarity(anchor, n)) for n in negatives]
        filtered_negatives = [n for n, s in scored_negatives if s >= negative_threshold]
        filtered_negatives = sorted(filtered_negatives, key=lambda n: next(s for nn, s in scored_negatives if nn == n), reverse=True)

        # Párování pozitiv s nejhorším skóre s negativy s nejhorším skóre
        min_count = min(len(filtered_positives), len(filtered_negatives))

        # Vybereme jen tolik párů, kolik máme menší ze skupin
        selected_pairs = list(zip(filtered_positives[:min_count], filtered_negatives[:min_count]))

        for positive, negative in selected_pairs:
            triplets.append({
                'anchor': anchor,
                'positive': positive,
                'negative': negative
            })

    df = pd.DataFrame(triplets)
    return df