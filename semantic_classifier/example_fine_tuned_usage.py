#!/usr/bin/env python3
"""
Příklad použití vyladěného modelu se SBertClassifier.

Ukazuje, jak:
1. <PERSON><PERSON><PERSON><PERSON> fine-tuning
2. <PERSON><PERSON><PERSON><PERSON><PERSON> vyla<PERSON> model s SBertClassifier
3. <PERSON><PERSON><PERSON> vý<PERSON><PERSON>ky před a po fine-tuningu
"""

import os
import sys
import tempfile
import shutil
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.SBertClassifier import SBertClassifier
from semantic_classifier.fine_tune_sbert import SBertFineTuner

def create_sample_training_data():
    """Vytvoří ukázková tréninková data pro demonstraci."""
    import pandas as pd
    
    # Ukázková data - faktury a související termíny
    training_data = {
        'Faktura': {
            'anchor': 'účetní doklad pro vyúčtování služeb',
            'positives': ['faktura', 'invoice', 'účet', 'vyúčtování'],
            'negatives': ['objednávka', 'smlouva', 'nabídka', 'reklamace']
        },
        'Datum_vystaveni': {
            'anchor': 'den kdy byl doklad vytvořen',
            'positives': ['datum vystavení', 'issue date', 'vystaveno dne', 'datum vytvoření'],
            'negatives': ['datum splatnosti', 'due date', 'termín dodání', 'datum narození']
        },
        'Castka': {
            'anchor': 'peněžní hodnota k úhradě',
            'positives': ['částka', 'amount', 'suma', 'cena', 'hodnota'],
            'negatives': ['datum', 'číslo', 'název', 'adresa', 'telefon']
        },
        'Dodavatel': {
            'anchor': 'subjekt poskytující služby nebo zboží',
            'positives': ['dodavatel', 'supplier', 'prodávající', 'poskytovatel'],
            'negatives': ['odběratel', 'customer', 'kupující', 'klient']
        }
    }
    
    # Vytvoř dočasný Excel soubor
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_path = temp_file.name
    temp_file.close()
    
    with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
        for category_name, data in training_data.items():
            rows = []
            
            # Kotva
            rows.append({
                'anchor_triplet': data['anchor'],
                'positives_variants': '',
                'negatives': ''
            })
            
            # Pozitivní příklady
            for positive in data['positives']:
                rows.append({
                    'anchor_triplet': '',
                    'positives_variants': positive,
                    'negatives': ''
                })
            
            # Negativní příklady
            for negative in data['negatives']:
                rows.append({
                    'anchor_triplet': '',
                    'positives_variants': '',
                    'negatives': negative
                })
            
            df = pd.DataFrame(rows)
            df.to_excel(writer, sheet_name=category_name, index=False)
    
    return temp_path

def test_original_model():
    """Test původního (nevyladěného) modelu."""
    print("=== Test původního modelu ===")
    
    # Vytvoříme SBertClassifier s původním modelem
    classifier = SBertClassifier()
    
    # Testovací data
    class_texts = {
        "Faktura": ["faktura", "invoice", "účet"],
        "Datum": ["datum vystavení", "issue date", "vystaveno"],
        "Částka": ["částka", "amount", "suma", "cena"],
        "Dodavatel": ["dodavatel", "supplier", "prodávající"]
    }
    
    classifier.create_and_save_prototypes(class_texts)
    
    # Test klasifikace
    test_texts = [
        "invoice number 2024001",
        "issue date 15.01.2024", 
        "total amount 1500 EUR",
        "supplier ABC Ltd.",
        "neznámý text xyz"
    ]
    
    print("Výsledky původního modelu:")
    original_results = []
    for text in test_texts:
        class_id, confidence = classifier.classify(text)
        class_name = classifier.get_class_name(class_id)
        original_results.append((text, class_name, confidence))
        print(f"  '{text}' -> {class_name} (confidence: {confidence:.3f})")
    
    return original_results

def perform_fine_tuning(training_data_path):
    """Provede fine-tuning a vrátí cestu k vyladěnému modelu."""
    print("\n=== Fine-tuning modelu ===")
    
    # Vytvoříme dočasnou složku pro výstup
    output_dir = tempfile.mkdtemp(prefix="fine_tuned_demo_")
    
    try:
        # Inicializace fine-tuneru
        fine_tuner = SBertFineTuner(device="cpu")
        
        # Spustíme fine-tuning (krátký pro demo)
        print("Spouštím fine-tuning (2 epochy pro demo)...")
        fine_tuner.fine_tune(
            xlsx_path=training_data_path,
            output_dir=output_dir,
            epochs=2,  # Krátký pro demo
            batch_size=4,
            warmup_steps=5,
            evaluation_steps=10
        )
        
        print(f"✅ Fine-tuning dokončen: {output_dir}")
        return output_dir
        
    except Exception as e:
        print(f"❌ Fine-tuning selhal: {e}")
        # Vyčistíme při chybě
        try:
            shutil.rmtree(output_dir)
        except:
            pass
        raise

def test_fine_tuned_model(model_path):
    """Test vyladěného modelu."""
    print(f"\n=== Test vyladěného modelu ===")
    
    # Vytvoříme SBertClassifier s vyladěným modelem
    classifier = SBertClassifier(
        model_name=model_path,  # Použijeme vyladěný model
        model_dir=model_path
    )
    
    # Stejná testovací data jako u původního modelu
    class_texts = {
        "Faktura": ["faktura", "invoice", "účet"],
        "Datum": ["datum vystavení", "issue date", "vystaveno"],
        "Částka": ["částka", "amount", "suma", "cena"],
        "Dodavatel": ["dodavatel", "supplier", "prodávající"]
    }
    
    classifier.create_and_save_prototypes(class_texts)
    
    # Test klasifikace
    test_texts = [
        "invoice number 2024001",
        "issue date 15.01.2024", 
        "total amount 1500 EUR",
        "supplier ABC Ltd.",
        "neznámý text xyz"
    ]
    
    print("Výsledky vyladěného modelu:")
    fine_tuned_results = []
    for text in test_texts:
        class_id, confidence = classifier.classify(text)
        class_name = classifier.get_class_name(class_id)
        fine_tuned_results.append((text, class_name, confidence))
        print(f"  '{text}' -> {class_name} (confidence: {confidence:.3f})")
    
    return fine_tuned_results

def compare_results(original_results, fine_tuned_results):
    """Porovná výsledky původního a vyladěného modelu."""
    print("\n=== Porovnání výsledků ===")
    
    print("| Text | Původní | Vyladěný | Zlepšení |")
    print("|------|---------|----------|----------|")
    
    for (text, orig_class, orig_conf), (_, tuned_class, tuned_conf) in zip(original_results, fine_tuned_results):
        improvement = tuned_conf - orig_conf
        improvement_str = f"{improvement:+.3f}"
        
        # Zkrátíme text pro tabulku
        short_text = text[:20] + "..." if len(text) > 20 else text
        
        print(f"| {short_text:<23} | {orig_class:<8} ({orig_conf:.3f}) | {tuned_class:<8} ({tuned_conf:.3f}) | {improvement_str} |")
    
    # Celkové statistiky
    orig_avg = sum(conf for _, _, conf in original_results) / len(original_results)
    tuned_avg = sum(conf for _, _, conf in fine_tuned_results) / len(fine_tuned_results)
    
    print(f"\n📊 Průměrná confidence:")
    print(f"  Původní model: {orig_avg:.3f}")
    print(f"  Vyladěný model: {tuned_avg:.3f}")
    print(f"  Celkové zlepšení: {tuned_avg - orig_avg:+.3f}")

def main():
    """Hlavní demonstrační funkce."""
    print("🎯 DEMONSTRACE FINE-TUNING + SBertClassifier")
    print("=" * 60)
    
    training_data_path = None
    fine_tuned_model_path = None
    
    try:
        # 1. Vytvoříme tréninková data
        print("Vytvářím ukázková tréninková data...")
        training_data_path = create_sample_training_data()
        
        # 2. Test původního modelu
        original_results = test_original_model()
        
        # 3. Zeptáme se, zda spustit fine-tuning
        response = input("\nSpustit fine-tuning? (může trvat několik minut) [y/N]: ")
        if response.lower() not in ['y', 'yes']:
            print("Fine-tuning přeskočen.")
            return
        
        # 4. Fine-tuning
        fine_tuned_model_path = perform_fine_tuning(training_data_path)
        
        # 5. Test vyladěného modelu
        fine_tuned_results = test_fine_tuned_model(fine_tuned_model_path)
        
        # 6. Porovnání výsledků
        compare_results(original_results, fine_tuned_results)
        
        print("\n✅ Demonstrace dokončena!")
        print(f"💡 Vyladěný model je uložen v: {fine_tuned_model_path}")
        print("💡 Můžete ho použít s SBertClassifier(model_name=path)")
        
    except Exception as e:
        print(f"\n❌ Chyba během demonstrace: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Vyčištění
        if training_data_path:
            try:
                os.unlink(training_data_path)
                print("🧹 Tréninková data smazána")
            except:
                pass
        
        # Vyladěný model necháme - uživatel ho může chtít použít
        if fine_tuned_model_path:
            print(f"📁 Vyladěný model ponechán v: {fine_tuned_model_path}")

if __name__ == "__main__":
    main()
