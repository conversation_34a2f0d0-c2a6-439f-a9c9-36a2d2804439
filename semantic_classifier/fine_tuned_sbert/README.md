---
tags:
- sentence-transformers
- sentence-similarity
- feature-extraction
- dense
- generated_from_trainer
- dataset_size:153
- loss:TripletLoss
base_model: sentence-transformers/paraphrase-multilingual-mpnet-base-v2
widget:
- source_sentence: subjekt poskytu<PERSON><PERSON><PERSON><PERSON> zboží nebo služby
  sentences:
  - v s
  - Dodavatel
  - Kupující
- source_sentence: termín pro úhradu finanční povinnosti
  sentences:
  - splatné do
  - Due Date
  - datum vytvoření dokumentu
- source_sentence: individuální cenové zvýhodnění
  sentences:
  - zálohová faktura
  - Sconto
  - order no.
- source_sentence: doklad pro platbu před dodáním plnění
  sentences:
  - p<PERSON><PERSON><PERSON><PERSON>ce faktury
  - datum vystavení faktury
  - proforma faktura
- source_sentence: č<PERSON>lo dokladu typu objednávka
  sentences:
  - Order No.
  - ZÁKLAD DANĚ
  - číslo dl.
pipeline_tag: sentence-similarity
library_name: sentence-transformers
metrics:
- cosine_accuracy
- cosine_accuracy_threshold
- cosine_f1
- cosine_f1_threshold
- cosine_precision
- cosine_recall
- cosine_ap
- cosine_mcc
model-index:
- name: SentenceTransformer based on sentence-transformers/paraphrase-multilingual-mpnet-base-v2
  results:
  - task:
      type: binary-classification
      name: Binary Classification
    dataset:
      name: val
      type: val
    metrics:
    - type: cosine_accuracy
      value: 0.5128205128205128
      name: Cosine Accuracy
    - type: cosine_accuracy_threshold
      value: 0.5583364963531494
      name: Cosine Accuracy Threshold
    - type: cosine_f1
      value: 0.6551724137931034
      name: Cosine F1
    - type: cosine_f1_threshold
      value: 0.1497989445924759
      name: Cosine F1 Threshold
    - type: cosine_precision
      value: 0.4935064935064935
      name: Cosine Precision
    - type: cosine_recall
      value: 0.9743589743589743
      name: Cosine Recall
    - type: cosine_ap
      value: 0.4377368325904786
      name: Cosine Ap
    - type: cosine_mcc
      value: -0.11396057645963796
      name: Cosine Mcc
---

# SentenceTransformer based on sentence-transformers/paraphrase-multilingual-mpnet-base-v2

This is a [sentence-transformers](https://www.SBERT.net) model finetuned from [sentence-transformers/paraphrase-multilingual-mpnet-base-v2](https://huggingface.co/sentence-transformers/paraphrase-multilingual-mpnet-base-v2). It maps sentences & paragraphs to a 768-dimensional dense vector space and can be used for semantic textual similarity, semantic search, paraphrase mining, text classification, clustering, and more.

## Model Details

### Model Description
- **Model Type:** Sentence Transformer
- **Base model:** [sentence-transformers/paraphrase-multilingual-mpnet-base-v2](https://huggingface.co/sentence-transformers/paraphrase-multilingual-mpnet-base-v2) <!-- at revision 4328cf26390c98c5e3c738b4460a05b95f4911f5 -->
- **Maximum Sequence Length:** 512 tokens
- **Output Dimensionality:** 768 dimensions
- **Similarity Function:** Cosine Similarity
<!-- - **Training Dataset:** Unknown -->
<!-- - **Language:** Unknown -->
<!-- - **License:** Unknown -->

### Model Sources

- **Documentation:** [Sentence Transformers Documentation](https://sbert.net)
- **Repository:** [Sentence Transformers on GitHub](https://github.com/UKPLab/sentence-transformers)
- **Hugging Face:** [Sentence Transformers on Hugging Face](https://huggingface.co/models?library=sentence-transformers)

### Full Model Architecture

```
SentenceTransformer(
  (0): Transformer({'max_seq_length': 512, 'do_lower_case': False, 'architecture': 'XLMRobertaModel'})
  (1): Pooling({'word_embedding_dimension': 768, 'pooling_mode_cls_token': False, 'pooling_mode_mean_tokens': True, 'pooling_mode_max_tokens': False, 'pooling_mode_mean_sqrt_len_tokens': False, 'pooling_mode_weightedmean_tokens': False, 'pooling_mode_lasttoken': False, 'include_prompt': True})
  (2): Normalize()
)
```

## Usage

### Direct Usage (Sentence Transformers)

First install the Sentence Transformers library:

```bash
pip install -U sentence-transformers
```

Then you can load this model and run inference.
```python
from sentence_transformers import SentenceTransformer

# Download from the 🤗 Hub
model = SentenceTransformer("sentence_transformers_model_id")
# Run inference
sentences = [
    'číslo dokladu typu objednávka',
    'Order No.',
    'číslo dl.',
]
embeddings = model.encode(sentences)
print(embeddings.shape)
# [3, 768]

# Get the similarity scores for the embeddings
similarities = model.similarity(embeddings, embeddings)
print(similarities)
# tensor([[1.0000, 0.6311, 0.6255],
#         [0.6311, 1.0000, 0.5373],
#         [0.6255, 0.5373, 1.0000]])
```

<!--
### Direct Usage (Transformers)

<details><summary>Click to see the direct usage in Transformers</summary>

</details>
-->

<!--
### Downstream Usage (Sentence Transformers)

You can finetune this model on your own dataset.

<details><summary>Click to expand</summary>

</details>
-->

<!--
### Out-of-Scope Use

*List how the model may foreseeably be misused and address what users ought not to do with the model.*
-->

## Evaluation

### Metrics

#### Binary Classification

* Dataset: `val`
* Evaluated with [<code>BinaryClassificationEvaluator</code>](https://sbert.net/docs/package_reference/sentence_transformer/evaluation.html#sentence_transformers.evaluation.BinaryClassificationEvaluator)

| Metric                    | Value      |
|:--------------------------|:-----------|
| cosine_accuracy           | 0.5128     |
| cosine_accuracy_threshold | 0.5583     |
| cosine_f1                 | 0.6552     |
| cosine_f1_threshold       | 0.1498     |
| cosine_precision          | 0.4935     |
| cosine_recall             | 0.9744     |
| **cosine_ap**             | **0.4377** |
| cosine_mcc                | -0.114     |

<!--
## Bias, Risks and Limitations

*What are the known or foreseeable issues stemming from this model? You could also flag here known failure cases or weaknesses of the model.*
-->

<!--
### Recommendations

*What are recommendations with respect to the foreseeable issues? For example, filtering explicit content.*
-->

## Training Details

### Training Dataset

#### Unnamed Dataset

* Size: 153 training samples
* Columns: <code>sentence_0</code>, <code>sentence_1</code>, and <code>sentence_2</code>
* Approximate statistics based on the first 153 samples:
  |         | sentence_0                                                                        | sentence_1                                                                       | sentence_2                                                                       |
  |:--------|:----------------------------------------------------------------------------------|:---------------------------------------------------------------------------------|:---------------------------------------------------------------------------------|
  | type    | string                                                                            | string                                                                           | string                                                                           |
  | details | <ul><li>min: 6 tokens</li><li>mean: 12.51 tokens</li><li>max: 29 tokens</li></ul> | <ul><li>min: 3 tokens</li><li>mean: 4.93 tokens</li><li>max: 12 tokens</li></ul> | <ul><li>min: 3 tokens</li><li>mean: 5.15 tokens</li><li>max: 12 tokens</li></ul> |
* Samples:
  | sentence_0                                                                                         | sentence_1                              | sentence_2                        |
  |:---------------------------------------------------------------------------------------------------|:----------------------------------------|:----------------------------------|
  | <code>účetní a daňový doklad kterým dodavatel fakturuje odběrateli dodané zboží nebo služby</code> | <code>zjednodušený daňový doklad</code> | <code>potvrzení objednávky</code> |
  | <code>sazba daně z přidané hodnoty</code>                                                          | <code>sazba DPH</code>                  | <code>DPH</code>                  |
  | <code>variabilní kód dokladu</code>                                                                | <code>var. symbol</code>                | <code>netto</code>                |
* Loss: [<code>TripletLoss</code>](https://sbert.net/docs/package_reference/sentence_transformer/losses.html#tripletloss) with these parameters:
  ```json
  {
      "distance_metric": "TripletDistanceMetric.COSINE",
      "triplet_margin": 0.3
  }
  ```

### Training Hyperparameters
#### Non-Default Hyperparameters

- `eval_strategy`: steps
- `per_device_train_batch_size`: 128
- `per_device_eval_batch_size`: 128
- `num_train_epochs`: 100
- `multi_dataset_batch_sampler`: round_robin

#### All Hyperparameters
<details><summary>Click to expand</summary>

- `overwrite_output_dir`: False
- `do_predict`: False
- `eval_strategy`: steps
- `prediction_loss_only`: True
- `per_device_train_batch_size`: 128
- `per_device_eval_batch_size`: 128
- `per_gpu_train_batch_size`: None
- `per_gpu_eval_batch_size`: None
- `gradient_accumulation_steps`: 1
- `eval_accumulation_steps`: None
- `torch_empty_cache_steps`: None
- `learning_rate`: 5e-05
- `weight_decay`: 0.0
- `adam_beta1`: 0.9
- `adam_beta2`: 0.999
- `adam_epsilon`: 1e-08
- `max_grad_norm`: 1
- `num_train_epochs`: 100
- `max_steps`: -1
- `lr_scheduler_type`: linear
- `lr_scheduler_kwargs`: {}
- `warmup_ratio`: 0.0
- `warmup_steps`: 0
- `log_level`: passive
- `log_level_replica`: warning
- `log_on_each_node`: True
- `logging_nan_inf_filter`: True
- `save_safetensors`: True
- `save_on_each_node`: False
- `save_only_model`: False
- `restore_callback_states_from_checkpoint`: False
- `no_cuda`: False
- `use_cpu`: False
- `use_mps_device`: False
- `seed`: 42
- `data_seed`: None
- `jit_mode_eval`: False
- `use_ipex`: False
- `bf16`: False
- `fp16`: False
- `fp16_opt_level`: O1
- `half_precision_backend`: auto
- `bf16_full_eval`: False
- `fp16_full_eval`: False
- `tf32`: None
- `local_rank`: 0
- `ddp_backend`: None
- `tpu_num_cores`: None
- `tpu_metrics_debug`: False
- `debug`: []
- `dataloader_drop_last`: False
- `dataloader_num_workers`: 0
- `dataloader_prefetch_factor`: None
- `past_index`: -1
- `disable_tqdm`: False
- `remove_unused_columns`: True
- `label_names`: None
- `load_best_model_at_end`: False
- `ignore_data_skip`: False
- `fsdp`: []
- `fsdp_min_num_params`: 0
- `fsdp_config`: {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}
- `fsdp_transformer_layer_cls_to_wrap`: None
- `accelerator_config`: {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}
- `deepspeed`: None
- `label_smoothing_factor`: 0.0
- `optim`: adamw_torch_fused
- `optim_args`: None
- `adafactor`: False
- `group_by_length`: False
- `length_column_name`: length
- `ddp_find_unused_parameters`: None
- `ddp_bucket_cap_mb`: None
- `ddp_broadcast_buffers`: False
- `dataloader_pin_memory`: True
- `dataloader_persistent_workers`: False
- `skip_memory_metrics`: True
- `use_legacy_prediction_loop`: False
- `push_to_hub`: False
- `resume_from_checkpoint`: None
- `hub_model_id`: None
- `hub_strategy`: every_save
- `hub_private_repo`: None
- `hub_always_push`: False
- `hub_revision`: None
- `gradient_checkpointing`: False
- `gradient_checkpointing_kwargs`: None
- `include_inputs_for_metrics`: False
- `include_for_metrics`: []
- `eval_do_concat_batches`: True
- `fp16_backend`: auto
- `push_to_hub_model_id`: None
- `push_to_hub_organization`: None
- `mp_parameters`: 
- `auto_find_batch_size`: False
- `full_determinism`: False
- `torchdynamo`: None
- `ray_scope`: last
- `ddp_timeout`: 1800
- `torch_compile`: False
- `torch_compile_backend`: None
- `torch_compile_mode`: None
- `include_tokens_per_second`: False
- `include_num_input_tokens_seen`: False
- `neftune_noise_alpha`: None
- `optim_target_modules`: None
- `batch_eval_metrics`: False
- `eval_on_start`: False
- `use_liger_kernel`: False
- `liger_kernel_config`: None
- `eval_use_gather_object`: False
- `average_tokens_across_devices`: False
- `prompts`: None
- `batch_sampler`: batch_sampler
- `multi_dataset_batch_sampler`: round_robin
- `router_mapping`: {}
- `learning_rate_mapping`: {}

</details>

### Training Logs
| Epoch | Step | val_cosine_ap |
|:-----:|:----:|:-------------:|
| 1.0   | 2    | 0.3664        |
| 2.0   | 4    | 0.3664        |
| 3.0   | 6    | 0.3671        |
| 4.0   | 8    | 0.3678        |
| 5.0   | 10   | 0.3680        |
| 6.0   | 12   | 0.3686        |
| 7.0   | 14   | 0.3685        |
| 8.0   | 16   | 0.3684        |
| 9.0   | 18   | 0.3684        |
| 10.0  | 20   | 0.3690        |
| 11.0  | 22   | 0.3699        |
| 12.0  | 24   | 0.3701        |
| 13.0  | 26   | 0.3700        |
| 14.0  | 28   | 0.3697        |
| 15.0  | 30   | 0.3711        |
| 16.0  | 32   | 0.3733        |
| 17.0  | 34   | 0.3739        |
| 18.0  | 36   | 0.3747        |
| 19.0  | 38   | 0.3760        |
| 20.0  | 40   | 0.3774        |
| 21.0  | 42   | 0.3783        |
| 22.0  | 44   | 0.3789        |
| 23.0  | 46   | 0.3802        |
| 24.0  | 48   | 0.3806        |
| 25.0  | 50   | 0.3822        |
| 26.0  | 52   | 0.3846        |
| 27.0  | 54   | 0.3861        |
| 28.0  | 56   | 0.3883        |
| 29.0  | 58   | 0.3905        |
| 30.0  | 60   | 0.3918        |
| 31.0  | 62   | 0.3929        |
| 32.0  | 64   | 0.3958        |
| 33.0  | 66   | 0.3971        |
| 34.0  | 68   | 0.3999        |
| 35.0  | 70   | 0.4021        |
| 36.0  | 72   | 0.4051        |
| 37.0  | 74   | 0.4080        |
| 38.0  | 76   | 0.4102        |
| 39.0  | 78   | 0.4137        |
| 40.0  | 80   | 0.4156        |
| 41.0  | 82   | 0.4160        |
| 42.0  | 84   | 0.4186        |
| 43.0  | 86   | 0.4218        |
| 44.0  | 88   | 0.4261        |
| 45.0  | 90   | 0.4276        |
| 46.0  | 92   | 0.4278        |
| 47.0  | 94   | 0.4306        |
| 48.0  | 96   | 0.4321        |
| 49.0  | 98   | 0.4345        |
| 50.0  | 100  | 0.4377        |


### Framework Versions
- Python: 3.10.8
- Sentence Transformers: 5.1.0
- Transformers: 4.55.2
- PyTorch: 2.8.0
- Accelerate: 1.10.0
- Datasets: 4.0.0
- Tokenizers: 0.21.4

## Citation

### BibTeX

#### Sentence Transformers
```bibtex
@inproceedings{reimers-2019-sentence-bert,
    title = "Sentence-BERT: Sentence Embeddings using Siamese BERT-Networks",
    author = "Reimers, Nils and Gurevych, Iryna",
    booktitle = "Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing",
    month = "11",
    year = "2019",
    publisher = "Association for Computational Linguistics",
    url = "https://arxiv.org/abs/1908.10084",
}
```

#### TripletLoss
```bibtex
@misc{hermans2017defense,
    title={In Defense of the Triplet Loss for Person Re-Identification},
    author={Alexander Hermans and Lucas Beyer and Bastian Leibe},
    year={2017},
    eprint={1703.07737},
    archivePrefix={arXiv},
    primaryClass={cs.CV}
}
```

<!--
## Glossary

*Clearly define terms in order to be accessible across audiences.*
-->

<!--
## Model Card Authors

*Lists the people who create the model card, providing recognition and accountability for the detailed work that goes into its construction.*
-->

<!--
## Model Card Contact

*Provides a way for people who have updates to the Model Card, suggestions, or questions, to contact the Model Card authors.*
-->