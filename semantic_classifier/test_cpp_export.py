#!/usr/bin/env python3
"""
Test pokročilého C++ exportu pro SBertClassifier.
"""

import os
import sys
import numpy as np
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.SBertClassifier import SBertClassifier

def test_cpp_export():
    """Test pokročilého C++ exportu."""
    print("=== Test pokročilého C++ exportu ===")
    
    # 1. Inicializace a vytvoření prototypů
    classifier = SBertClassifier()
    
    # Malá sada testovacích dat
    class_texts = {
        "Faktura": ["faktura", "číslo faktury", "invoice"],
        "Datum": ["datum", "datum vystavení", "date"],
        "Částka": ["částka", "celkem", "amount", "total"]
    }
    
    classifier.create_and_save_prototypes(class_texts)
    
    # 2. Export bez ONNX modelu (rychlý)
    print("\n--- Export bez ONNX modelu ---")
    export_dir = "./semantic_classifier/cpp_export_test"
    classifier.export_for_cpp(export_dir, export_model=False)
    
    # 3. Ověření exportovaných souborů
    print("\n--- Ověření exportovaných souborů ---")
    expected_files = [
        "prototypes.bin",
        "prototypes_dims.txt", 
        "prototypes_simple.bin",
        "model_config.h",
        "tokenizer_config.json"
    ]
    
    for filename in expected_files:
        filepath = os.path.join(export_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"✅ {filename}: {size} bytes")
        else:
            print(f"❌ {filename}: nenalezen")
    
    # 4. Test načtení raw prototypů
    print("\n--- Test načtení raw prototypů ---")
    raw_prototypes_path = os.path.join(export_dir, "prototypes.bin")
    dims_path = os.path.join(export_dir, "prototypes_dims.txt")
    
    if os.path.exists(raw_prototypes_path) and os.path.exists(dims_path):
        # Načti rozměry
        with open(dims_path, 'r') as f:
            num_classes, embedding_dim = map(int, f.read().strip().split())
        
        # Načti raw data
        raw_data = np.fromfile(raw_prototypes_path, dtype=np.float32)
        loaded_matrix = raw_data.reshape(num_classes, embedding_dim)
        
        print(f"Načteno: {loaded_matrix.shape} (očekáváno: {classifier.proto_matrix.shape})")
        
        # Porovnej s originálními prototypy
        if np.allclose(loaded_matrix, classifier.proto_matrix, rtol=1e-5):
            print("✅ Raw prototypy se shodují s originálními")
        else:
            print("❌ Raw prototypy se neshodují!")
            print(f"Max rozdíl: {np.max(np.abs(loaded_matrix - classifier.proto_matrix))}")
    
    # 5. Test načtení jednoduchých prototypů
    print("\n--- Test načtení jednoduchých prototypů ---")
    simple_path = os.path.join(export_dir, "prototypes_simple.bin")
    
    if os.path.exists(simple_path):
        with open(simple_path, 'rb') as f:
            # Načti header
            num_classes = int.from_bytes(f.read(4), 'little')
            embedding_dim = int.from_bytes(f.read(4), 'little')
            
            print(f"Header: {num_classes} tříd, {embedding_dim} dimenzí")
            
            # Načti třídy
            loaded_names = []
            loaded_embeddings = []
            
            for i in range(num_classes):
                name_len = int.from_bytes(f.read(4), 'little')
                name = f.read(name_len).decode('utf-8')
                embedding_bytes = f.read(embedding_dim * 4)
                embedding = np.frombuffer(embedding_bytes, dtype=np.float32)
                
                loaded_names.append(name)
                loaded_embeddings.append(embedding)
            
            print(f"Načtené třídy: {loaded_names}")
            
            # Porovnej s originálními
            if loaded_names == classifier.class_names:
                print("✅ Názvy tříd se shodují")
            else:
                print("❌ Názvy tříd se neshodují!")
            
            loaded_matrix = np.array(loaded_embeddings)
            if np.allclose(loaded_matrix, classifier.proto_matrix, rtol=1e-5):
                print("✅ Jednoduché prototypy se shodují s originálními")
            else:
                print("❌ Jednoduché prototypy se neshodují!")
    
    # 6. Zobrazení C++ header
    print("\n--- C++ Header ---")
    header_path = os.path.join(export_dir, "model_config.h")
    if os.path.exists(header_path):
        with open(header_path, 'r') as f:
            lines = f.readlines()
        
        print("První 20 řádků C++ header:")
        for i, line in enumerate(lines[:20]):
            print(f"{i+1:2d}: {line.rstrip()}")
        
        if len(lines) > 20:
            print(f"... (celkem {len(lines)} řádků)")
    
    print(f"\n✅ Test dokončen. Export v: {export_dir}")

def test_with_onnx():
    """Test s ONNX exportem (může být pomalý)."""
    print("\n=== Test s ONNX exportem ===")
    
    try:
        classifier = SBertClassifier()
        
        # Malá sada dat
        class_texts = {
            "Test1": ["test jeden", "first test"],
            "Test2": ["test dva", "second test"]
        }
        
        classifier.create_and_save_prototypes(class_texts)
        
        # Export s ONNX
        export_dir = "./semantic_classifier/cpp_export_onnx_test"
        classifier.export_for_cpp(export_dir, export_model=True)
        
        # Ověř ONNX soubor
        onnx_path = os.path.join(export_dir, "text_encoder.onnx")
        if os.path.exists(onnx_path):
            size_mb = os.path.getsize(onnx_path) / (1024 * 1024)
            print(f"✅ ONNX model: {size_mb:.1f} MB")
        else:
            print("❌ ONNX model nebyl vytvořen")
        
        print(f"✅ ONNX test dokončen. Export v: {export_dir}")
        
    except Exception as e:
        print(f"⚠️  ONNX test selhal: {e}")
        print("💡 Možná chybí torch nebo onnx balíček")

if __name__ == "__main__":
    try:
        test_cpp_export()
        
        # ONNX test je volitelný (může být pomalý)
        response = input("\nSpustit také ONNX test? (může být pomalý) [y/N]: ")
        if response.lower() in ['y', 'yes']:
            test_with_onnx()
        
    except Exception as e:
        print(f"❌ Chyba během testování: {e}")
        import traceback
        traceback.print_exc()
