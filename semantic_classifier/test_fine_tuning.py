#!/usr/bin/env python3
"""
Test script pro fine-tuning funkcionalitu.
"""

import os
import sys
import tempfile
import pandas as pd
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.fine_tune_sbert import SBertFineTuner
from semantic_classifier.data_util import generate_triplet_dataset

def create_test_excel():
    """Vytvoří malý testovací Excel soubor pro fine-tuning."""
    
    # Malá sada testovacích dat
    test_data = {
        'Faktura': {
            'anchor': 'účetní doklad pro fakturaci',
            'positives': ['faktura', 'invoice', 'účet za služby'],
            'negatives': ['objednávka', 'dodací list', 'smlouva']
        },
        'Datum': {
            'anchor': 'časové označení události',
            'positives': ['datum', 'date', 'čas', 'termín'],
            'negatives': ['částka', 'číslo', 'n<PERSON>zev', 'adresa']
        },
        'Částka': {
            'anchor': 'pen<PERSON><PERSON><PERSON><PERSON> hodnota',
            'positives': ['částka', 'amount', 'suma', 'cena'],
            'negatives': ['datum', 'jméno', 'adresa', 'telefon']
        }
    }
    
    # Vytvoř dočasný Excel soubor
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_path = temp_file.name
    temp_file.close()
    
    with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
        for category_name, data in test_data.items():
            rows = []
            
            # Kotva
            rows.append({
                'anchor_triplet': data['anchor'],
                'positives_variants': '',
                'negatives': ''
            })
            
            # Pozitivní příklady
            for positive in data['positives']:
                rows.append({
                    'anchor_triplet': '',
                    'positives_variants': positive,
                    'negatives': ''
                })
            
            # Negativní příklady
            for negative in data['negatives']:
                rows.append({
                    'anchor_triplet': '',
                    'positives_variants': '',
                    'negatives': negative
                })
            
            df = pd.DataFrame(rows)
            df.to_excel(writer, sheet_name=category_name, index=False)
    
    return temp_path

def test_data_loading():
    """Test načítání dat pomocí generate_triplet_dataset."""
    print("=== Test načítání dat ===")
    
    test_excel_path = create_test_excel()
    
    try:
        # Test generate_triplet_dataset
        print("Testování generate_triplet_dataset...")
        triplet_df = generate_triplet_dataset(test_excel_path)
        
        print(f"✅ Načteno {len(triplet_df)} tripletů")
        print(f"📊 Sloupce: {list(triplet_df.columns)}")
        
        # Zobrazíme ukázku
        print("\n📝 Ukázka tripletů:")
        for i in range(min(3, len(triplet_df))):
            row = triplet_df.iloc[i]
            print(f"  {i+1}. Anchor: '{row['anchor']}'")
            print(f"     Positive: '{row['positive']}'")
            print(f"     Negative: '{row['negative']}'")
            print()
        
        return test_excel_path, triplet_df
        
    except Exception as e:
        print(f"❌ Chyba při načítání dat: {e}")
        try:
            os.unlink(test_excel_path)
        except:
            pass
        raise

def test_fine_tuner_init():
    """Test inicializace SBertFineTuner."""
    print("=== Test inicializace SBertFineTuner ===")
    
    try:
        fine_tuner = SBertFineTuner(device="cpu")
        print("✅ SBertFineTuner úspěšně inicializován")
        return fine_tuner
        
    except Exception as e:
        print(f"❌ Chyba při inicializaci: {e}")
        raise

def test_data_preparation(fine_tuner, test_excel_path):
    """Test přípravy dat pro trénování."""
    print("=== Test přípravy dat ===")
    
    try:
        # Načteme data
        fine_tuner.load_data(test_excel_path)
        
        # Připravíme model
        fine_tuner.prepare_model()
        
        # Připravíme tréninková data
        train_examples, val_examples = fine_tuner.prepare_training_data(train_ratio=0.7)
        
        print(f"✅ Připraveno {len(train_examples)} train + {len(val_examples)} val příkladů")
        
        # Zkontrolujeme strukturu
        if train_examples:
            example = train_examples[0]
            print(f"📝 Ukázka InputExample: {len(example.texts)} textů")
            for i, text in enumerate(example.texts):
                print(f"  {i}: '{text}'")
        
        return train_examples, val_examples
        
    except Exception as e:
        print(f"❌ Chyba při přípravě dat: {e}")
        raise

def test_mini_fine_tuning(fine_tuner, test_excel_path):
    """Test mini fine-tuningu (1 epocha)."""
    print("=== Test mini fine-tuning ===")
    
    try:
        # Vytvoříme dočasnou složku pro výstup
        temp_output = tempfile.mkdtemp(prefix="fine_tuned_test_")
        
        print(f"Výstupní složka: {temp_output}")
        
        # Spustíme velmi krátký fine-tuning
        output_dir = fine_tuner.fine_tune(
            xlsx_path=test_excel_path,
            output_dir=temp_output,
            epochs=1,  # Pouze 1 epocha pro test
            batch_size=2,  # Malý batch
            warmup_steps=1,
            evaluation_steps=10
        )
        
        print(f"✅ Mini fine-tuning dokončen: {output_dir}")
        
        # Zkontrolujeme, že model byl uložen
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"📁 Soubory v output složce: {files}")
            
            # Zkontrolujeme klíčové soubory
            expected_files = ['config.json', 'pytorch_model.bin']
            for file in expected_files:
                if any(file in f for f in files):
                    print(f"✅ Nalezen {file}")
                else:
                    print(f"⚠️  Nenalezen {file}")
        
        return output_dir
        
    except Exception as e:
        print(f"❌ Chyba při mini fine-tuning: {e}")
        import traceback
        traceback.print_exc()
        raise

def test_model_evaluation(fine_tuner, model_path, test_excel_path):
    """Test evaluace vyladěného modelu."""
    print("=== Test evaluace modelu ===")
    
    try:
        fine_tuner.evaluate_model(model_path, test_excel_path)
        print("✅ Evaluace dokončena")
        
    except Exception as e:
        print(f"❌ Chyba při evaluaci: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Hlavní test funkce."""
    print("🧪 TESTOVÁNÍ FINE-TUNING FUNKCIONALITY")
    print("=" * 60)
    
    test_excel_path = None
    fine_tuner = None
    output_dir = None
    
    try:
        # 1. Test načítání dat
        test_excel_path, triplet_df = test_data_loading()
        
        # 2. Test inicializace
        fine_tuner = test_fine_tuner_init()
        
        # 3. Test přípravy dat
        train_examples, val_examples = test_data_preparation(fine_tuner, test_excel_path)
        
        # 4. Test mini fine-tuning (volitelný - může být pomalý)
        response = input("\nSpustit mini fine-tuning test? (může trvat několik minut) [y/N]: ")
        if response.lower() in ['y', 'yes']:
            output_dir = test_mini_fine_tuning(fine_tuner, test_excel_path)
            
            # 5. Test evaluace
            if output_dir:
                test_model_evaluation(fine_tuner, output_dir, test_excel_path)
        
        print("\n✅ Všechny testy dokončeny úspěšně!")
        
    except Exception as e:
        print(f"\n❌ Test selhal: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Vyčištění
        if test_excel_path:
            try:
                os.unlink(test_excel_path)
                print(f"🧹 Dočasný Excel soubor smazán")
            except:
                pass
        
        if output_dir:
            try:
                import shutil
                shutil.rmtree(output_dir)
                print(f"🧹 Dočasná output složka smazána")
            except:
                pass

if __name__ == "__main__":
    main()
