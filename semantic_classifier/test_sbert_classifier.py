#!/usr/bin/env python3
"""
Test script pro SBertClassifier.
Testuje základní funkčnost: inicializaci, vytvoření prototypů, klasifika<PERSON>.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.SBertClassifier import SBertClassifier

def test_basic_functionality():
    """Test základní funkčnosti."""
    print("=== Test základní funkčnosti SBertClassifier ===")
    
    # 1. Inicializace
    print("1. Inicializace klasifikátoru...")
    classifier = SBertClassifier()
    classifier.info()
    
    # 2. Test embeddingů
    print("\n2. Test výpočtu embeddingů...")
    test_texts = ["Číslo faktury", "Datum vystavení", "Celková částka"]
    embeddings = classifier.encode(test_texts)
    print(f"Embeddingy: shape={embeddings.shape}, dtype={embeddings.dtype}")
    
    # 3. Test podobnosti
    print("\n3. Test podobnosti textů...")
    similarity = classifier.similarity("faktura", "číslo faktury")
    print(f"Podobnost 'faktura' vs 'číslo faktury': {similarity:.3f}")
    
    # 4. Vytvoření prototypů
    print("\n4. Vytvoření prototypů...")
    class_texts = {
        "Faktura": ["faktura", "číslo faktury", "invoice number"],
        "Datum": ["datum", "datum vystavení", "date", "datum splatnosti"],
        "Částka": ["částka", "celkem", "amount", "total", "suma"]
    }
    
    classifier.create_and_save_prototypes(class_texts)
    classifier.info()
    
    # 5. Test klasifikace
    print("\n5. Test klasifikace...")
    test_cases = [
        "číslo faktury 2024001",
        "datum vystavení 15.1.2024", 
        "celková částka 1500 Kč",
        "invoice total",
        "neznámý text xyz"
    ]
    
    for text in test_cases:
        class_id, confidence = classifier.classify(text)
        class_name = classifier.get_class_name(class_id)
        print(f"'{text}' -> {class_name} (ID: {class_id}, confidence: {confidence:.3f})")
    
    # 6. Test batch klasifikace
    print("\n6. Test batch klasifikace...")
    batch_results = classifier.classify_batch(test_cases)
    print("Batch výsledky:")
    for text, (class_id, confidence) in zip(test_cases, batch_results):
        class_name = classifier.get_class_name(class_id)
        print(f"  '{text}' -> {class_name} (confidence: {confidence:.3f})")
    
    # 7. Export pro C++
    print("\n7. Export pro C++...")
    export_path = os.path.join(classifier.model_dir, "prototypes_cpp.bin")
    classifier.export_for_cpp(export_path)
    
    print("\n✅ Všechny testy dokončeny úspěšně!")

def test_reload():
    """Test znovunačtení prototypů."""
    print("\n=== Test znovunačtení prototypů ===")
    
    # Vytvoříme nový klasifikátor - měl by automaticky načíst prototypy
    classifier2 = SBertClassifier()
    classifier2.info()
    
    if classifier2.proto_matrix is not None:
        print("✅ Prototypy byly automaticky načteny!")
        
        # Test klasifikace
        class_id, confidence = classifier2.classify("číslo faktury")
        class_name = classifier2.get_class_name(class_id)
        print(f"Test klasifikace: 'číslo faktury' -> {class_name} (confidence: {confidence:.3f})")
    else:
        print("❌ Prototypy nebyly načteny!")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_reload()
    except Exception as e:
        print(f"❌ Chyba během testování: {e}")
        import traceback
        traceback.print_exc()
